import { Picker } from '@react-native-picker/picker';
import { useState } from 'react';
import {
    Button,
    FlatList,
    Image,
    Modal,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { useOutfitStore } from '../stores/outfitStore';

export default function CanvasScreen() {
  const { clothingItems, selectedItems, addToCanvas, clearSelection, saveOutfit } =
    useOutfitStore();

  const [showItems, setShowItems] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [outfitName, setOutfitName] = useState('');
  const [eventType, setEventType] = useState<'casual' | 'work' | 'vacation'>('casual');

  const handleSave = () => {
    saveOutfit({
      name: outfitName || `Outfit ${Date.now()}`,
      eventType,
    });
    setShowSaveModal(false);
    setOutfitName('');
    setEventType('casual');

    Toast.show({
      type: "success",
      text1: "Outfit saved successfully!",
      position: "bottom",
      bottomOffset: 40,
    });
  };

  return (
    <View style={styles.container}>
      {/* Canvas */}
      <View style={styles.canvas}>
        {selectedItems.map((item) => (
          <Image
            key={item.id}
            source={{ uri: item.uri }}
            style={[
              styles.clothingImage,
              {
                top:
                  item.type === 'cap'
                    ? 0
                    : item.type === 'shirt'
                    ? 90
                    : item.type === 'pant'
                    ? 190
                    : 190,
                zIndex:
                  item.type === 'cap'
                    ? 3
                    : item.type === 'shirt'
                    ? 2
                    : item.type === 'pant'
                    ? 1
                    : 0,
              },
            ]}
          />
        ))}
      </View>

      {/* Toggle Button */}
      <TouchableOpacity
        onPress={() => setShowItems(!showItems)}
        style={styles.toggleButton}
        activeOpacity={0.7}
      >
        <Text style={styles.toggleButtonText}>
          {showItems ? 'Hide Items' : 'Show Items'}
        </Text>
      </TouchableOpacity>

      {/* Available Items Panel */}
      {showItems && (
        <View style={styles.itemsPanel}>
          <Text style={styles.panelTitle}>Available Items:</Text>

          <FlatList
            data={clothingItems}
            keyExtractor={(item) => item.id}
            horizontal
            contentContainerStyle={styles.flatListContent}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.itemButton}
                onPress={() => addToCanvas(item)}
                activeOpacity={0.8}
              >
                <Image
                  source={{ uri: item.uri }}
                  style={styles.itemImage}
                />
                <Text style={styles.itemLabel}>{item.type}</Text>
              </TouchableOpacity>
            )}
          />

          {/* Action Buttons */}
          <View style={styles.actions}>
            <Button title="Clear Canvas" color="#2563eb" onPress={clearSelection} />
            <View style={{ height: 12 }} />
            <Button title="Save Outfit" color="#10b981" onPress={() => setShowSaveModal(true)} />
          </View>
        </View>
      )}

      {/* Save Outfit Modal */}
      <Modal
        visible={showSaveModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSaveModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Save Outfit</Text>

            <TextInput
              style={styles.input}
              placeholder="Outfit Name"
              value={outfitName}
              onChangeText={setOutfitName}
            />

            <Picker
              selectedValue={eventType}
              onValueChange={(value) => setEventType(value)}
              style={Platform.OS === 'web' ? { height: 40, width: '100%' } : undefined}
            >
              <Picker.Item label="Casual" value="casual" />
              <Picker.Item label="Work" value="work" />
              <Picker.Item label="Vacation" value="vacation" />
            </Picker>

            <View style={{ flexDirection: 'row', marginTop: 16, justifyContent: 'space-between' }}>
              <Button title="Cancel" color="#ccc" onPress={() => setShowSaveModal(false)} />
              <Button title="Save" color="#10b981" onPress={handleSave} />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8FA',
  },
  canvas: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    margin: 16,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 3,
    position: 'relative',
  },
  clothingImage: {
    position: 'absolute',
    width: 100,
    height: 100,
    left: '50%',
    marginLeft: -50,
  },
  toggleButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#2563eb',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2,
  },
  toggleButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  itemsPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: -2 },
    shadowRadius: 6,
    elevation: 5,
  },
  panelTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 12,
    color: '#111111',
  },
  flatListContent: {
    paddingVertical: 4,
  },
  itemButton: {
    marginRight: 16,
    alignItems: 'center',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  itemLabel: {
    marginTop: 6,
    fontSize: 12,
    color: '#333333',
    textTransform: 'capitalize',
  },
  actions: {
    marginTop: 20,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    width: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
  },
});
