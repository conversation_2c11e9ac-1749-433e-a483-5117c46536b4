import * as ImagePicker from 'expo-image-picker';
import { Platform } from 'react-native';

export async function pickImage(): Promise<string | undefined> {
  if (Platform.OS === 'web') {
    return new Promise<string | undefined>((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.onchange = () => {
        const file = input.files?.[0];
        if (file) resolve(URL.createObjectURL(file));
        else resolve(undefined);
      };
      input.click();
    });
  } else {
    const result = await ImagePicker.launchImageLibraryAsync({ mediaTypes: ImagePicker.MediaTypeOptions.Images });
    return result.canceled ? undefined : result.assets[0].uri;
  }
}