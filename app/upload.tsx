import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Toast from "react-native-toast-message";
import { useOutfitStore } from "../stores/outfitStore";
import { pickImage } from "../utils/imagePicker";

export default function UploadScreen() {
  const addClothingItem = useOutfitStore((s) => s.addClothingItem);

  const handlePick = async (type: "shirt" | "pant" | "cap") => {
    const uri = await pickImage();
    if (uri) {
      addClothingItem({
        id: Date.now().toString(),
        type,
        uri,
        name: `${type}-${Date.now()}`,
        position: { x: 50, y: 50 },
      });
      Toast.show({
        type: "success",
        text1: `${type} uploaded successfully!`,
        position: "bottom",
        bottomOffset: 40,
      });
    }
  };

  const clothingTypes = [
    { label: "Add Shirt", type: "shirt" },
    { label: "Add Pant", type: "pant" },
    { label: "Add Cap", type: "cap" },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Upload Your Clothing Items</Text>

      <View style={styles.buttonContainer}>
        {clothingTypes.map((item, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => handlePick(item.type as "shirt" | "pant" | "cap")}
            style={styles.button}
            activeOpacity={0.7}
          >
            <Text style={styles.buttonText}>{item.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 22,
    fontWeight: "600",
    color: "#111111",
    marginBottom: 32,
  },
  buttonContainer: {
    width: "100%",
    gap: 16,
  },
  button: {
    backgroundColor: "#F2F2F7",
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 16,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333333",
  },
});
