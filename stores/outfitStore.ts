import { create } from "zustand";
import { ClothingItem, Outfit } from "../models/types";

interface OutfitState {
  clothingItems: ClothingItem[];
  selectedItems: ClothingItem[];
  savedOutfits: Outfit[];
  addClothingItem: (item: ClothingItem) => void;
  addToCanvas: (item: ClothingItem) => void;
  saveOutfit: (payload: { name: string; eventType: string }) => void;
  clearSelection: () => void;
  loadOutfit: (outfit: Outfit) => void;
}

export const useOutfitStore = create<OutfitState>((set, get) => ({
  clothingItems: [],
  selectedItems: [],
  savedOutfits: [],

  addClothingItem: (item) =>
    set((state) => ({
      clothingItems: [...state.clothingItems, item],
    })),

  addToCanvas: (item) =>
    set((state) => ({
      selectedItems: [...state.selectedItems, item],
    })),

  saveOutfit: ({ name, eventType }) => {
    const newOutfit: Outfit = {
      id: Date.now().toString(),
      name,
      eventType,
      items: get().selectedItems,
      createdAt: new Date().toISOString(),
    };
    set((state) => ({
      savedOutfits: [...state.savedOutfits, newOutfit],
      selectedItems: [],
    }));
  },

  loadOutfit: (outfit: Outfit) => set({ selectedItems: outfit.items }),

  clearSelection: () => set({ selectedItems: [] }),
}));