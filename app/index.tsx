import { useRouter } from 'expo-router';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function HomeScreen() {
  const router = useRouter();

  const menuItems = [
    { title: 'Upload Items', route: '/upload' },
    { title: 'Go to Canvas', route: '/canvas' },
    { title: 'Saved Outfits', route: '/outfits' },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Outfit Mix & Match</Text>

      <View style={styles.buttonContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.button}
            onPress={() => router.push(item.route)}
            activeOpacity={0.7}
          >
            <Text style={styles.buttonText}>{item.title}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '600',
    color: '#111111',
    marginBottom: 40,
  },
  buttonContainer: {
    width: '100%',
    gap: 16, // If React Native version doesn't support `gap`, replace with marginBottom on buttons
  },
  button: {
    backgroundColor: '#F2F2F7',
    borderRadius: 16,
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 2, // For Android shadow
    marginBottom: 16, // Alternative to `gap`
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
});
