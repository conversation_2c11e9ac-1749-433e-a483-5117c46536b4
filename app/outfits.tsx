
import { useRouter } from 'expo-router';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Outfit } from "../models/types";
import { useOutfitStore } from '../stores/outfitStore';

export default function OutfitsScreen() {
  const outfits = useOutfitStore((s) => s.savedOutfits);
  const loadOutfit = useOutfitStore((s) => s.loadOutfit);
  const router = useRouter();

  const handleSelectOutfit = (outfit: Outfit) => {
    loadOutfit(outfit);
    router.push('/canvas');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Your Saved Outfits</Text>

      {outfits.length === 0 ? (
        <Text style={styles.emptyText}>You have no saved outfits yet. Start creating your first look!</Text>
      ) : (
        <FlatList
          data={outfits}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.card}
              onPress={() => handleSelectOutfit(item)}
              activeOpacity={0.8}
            >
              <Text style={styles.cardTitle}>{item.name}</Text>
              <Text style={styles.cardSub}>Event: {item.eventType}</Text>
              <Text style={styles.cardSub}>Items: {item.items.length}</Text>
              <Text style={styles.cardDate}>Saved on {new Date(item.createdAt).toLocaleString()}</Text>
            </TouchableOpacity>
          )}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 32,
    textAlign: 'center',
  },
  listContent: {
    paddingBottom: 24,
  },
  card: {
    backgroundColor: '#FFFFFF',
    padding: 18,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111111',
    marginBottom: 6,
  },
  cardSub: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 2,
  },
  cardDate: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
  },
});
